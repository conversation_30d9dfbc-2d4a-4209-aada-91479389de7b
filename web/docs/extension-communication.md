# Extension Communication Protocol

This document describes the message-based communication protocol between the web application and browser extension.

## Message Format

### From Web to Extension

```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: string,
  data?: Record<string, unknown>,
  requestId?: string
}
```

### From Extension to Web

```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: boolean,
  data?: ScriptData | null,
  error?: string,
  requestId?: string
}
```

## Supported Actions

### 1. ping

Check if extension is available.

**Request:**

```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'ping',
  requestId: 'req_123'
}
```

**Response:**

```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  requestId: 'req_123'
}
```

### 2. save-script

Save script to extension storage.

**Request:**

```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'save-script',
  data: {
    messageId: string,
    code: string,
    miniappId: number,
    name?: string,
    version?: number
  },
  requestId: 'req_123'
}
```

**Response:**

```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  requestId: 'req_123'
}
```

### 3. get-script

Retrieve script from extension storage.

**Request:**

```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'get-script',
  data: {
    miniappId: number
  },
  requestId: 'req_123'
}
```

**Response:**

```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  data: {
    code: string,
    version: number,
    updated_at: number
  },
  requestId: 'req_123'
}
```

### 4. delete-script

Delete script from extension storage.

**Request:**

```javascript
{
  type: 'FROM_WEB_TO_EXTENSION',
  action: 'delete-script',
  data: {
    miniappId: number
  },
  requestId: 'req_123'
}
```

**Response:**

```javascript
{
  type: 'FROM_EXTENSION_TO_WEB',
  success: true,
  requestId: 'req_123'
}
```

## Extension Implementation Example

```javascript
// In your extension's content script or background script
window.addEventListener('message', async event => {
  // Only accept messages from same origin
  if (event.origin !== window.location.origin) {
    return;
  }

  const message = event.data;
  if (message?.type === 'FROM_WEB_TO_EXTENSION') {
    try {
      let response = {
        type: 'FROM_EXTENSION_TO_WEB',
        success: true,
        requestId: message.requestId,
      };

      switch (message.action) {
        case 'ping':
          // Just respond with success
          break;

        case 'save-script':
          // Save to extension storage (e.g., chrome.storage.local)
          await saveScriptToStorage(message.data);
          break;

        case 'get-script':
          // Retrieve from extension storage
          const scriptData = await getScriptFromStorage(message.data.miniappId);
          response.data = scriptData;
          break;

        case 'delete-script':
          // Delete from extension storage
          await deleteScriptFromStorage(message.data.miniappId);
          break;

        default:
          response.success = false;
          response.error = `Unknown action: ${message.action}`;
      }

      // Send response back to web page
      window.postMessage(response, '*');
    } catch (error) {
      // Send error response
      window.postMessage(
        {
          type: 'FROM_EXTENSION_TO_WEB',
          success: false,
          error: error.message,
          requestId: message.requestId,
        },
        '*'
      );
    }
  }
});
```

## Fallback Behavior

When the extension is not available:

1. Scripts are saved to and retrieved from localStorage
2. Cross-tab synchronization still works via BroadcastChannel
3. No errors are thrown - the system gracefully degrades

## Troubleshooting

### Common Issues

1. **Extension request timeout**
   - Extension is not installed or not responding
   - Extension doesn't handle the message protocol
   - Check browser console for extension errors

2. **Permission error (403)**
   - Extension authentication failed
   - User needs to log in to the extension
   - API key might be invalid or expired

3. **No response from extension**
   - Extension content script not loaded
   - Extension manifest doesn't allow content script injection
   - Page origin not whitelisted in extension

### Debug Steps

1. Check if extension is responding:

   ```javascript
   import { extensionBridge } from '@/lib/extension-bridge';
   const isResponding = await extensionBridge.ping();
   console.log('Extension responding:', isResponding);
   ```

2. Monitor message flow in browser console:
   - Look for "Sending message to extension:" logs
   - Check for timeout errors
   - Verify extension is receiving messages

3. Extension should log received messages:
   ```javascript
   console.log('Extension received message:', message);
   ```

## Security Considerations

1. Messages are accepted from any origin (extension context)
2. Request IDs prevent message replay attacks
3. Timeouts prevent hanging requests (10 seconds)
4. All data is validated before processing
